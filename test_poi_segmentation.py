#!/usr/bin/env python3
"""
测试基于分词的POI搜索功能
"""

import os
from amap_mcp_tools import AmapMCPTools, mcp_search_poi, mcp_search_poi_with_segmentation

def test_word_segmentation():
    """测试分词功能"""
    print("=== 测试分词功能 ===")
    
    amap = AmapMCPTools()
    
    test_cases = [
        "方正大厦",
        "海淀医院", 
        "北京大学",
        "中关村软件园",
        "国贸中心",
        "首都机场",
        "清华科技园",
        "万达广场"
    ]
    
    for keyword in test_cases:
        segments = amap._simple_word_segment(keyword)
        print(f"'{keyword}' -> {segments}")
    
    print()

def test_poi_search_comparison():
    """对比普通搜索和分词搜索的结果"""
    print("=== 对比普通搜索和分词搜索 ===")
    
    keyword = "方正大厦"
    city = "北京市"
    
    print(f"搜索关键词: {keyword}")
    print(f"搜索城市: {city}")
    print()
    
    # 1. 普通POI搜索
    print("1. 普通POI搜索结果:")
    result1 = mcp_search_poi(keyword, city)
    
    if result1.get("status"):
        pois = result1["data"]["pois"]
        print(f"   找到 {len(pois)} 个结果:")
        for i, poi in enumerate(pois[:5], 1):  # 只显示前5个
            print(f"   {i}. {poi['name']} - {poi['address']}")
    else:
        print(f"   ❌ 搜索失败: {result1.get('error')}")
    
    print()
    
    # 2. 分词POI搜索（要求所有分词都匹配）
    print("2. 分词POI搜索结果（要求所有分词都匹配）:")
    result2 = mcp_search_poi_with_segmentation(keyword, city, require_all_segments=True)
    
    if result2.get("status"):
        data = result2["data"]
        print(f"   原始关键词: {data['original_keyword']}")
        print(f"   分词结果: {data['segments']}")
        print(f"   原始结果数: {data['original_count']}")
        print(f"   过滤后结果数: {data['filtered_count']}")
        print()
        
        pois = data["pois"]
        if pois:
            print("   过滤后的POI:")
            for i, poi in enumerate(pois[:5], 1):
                print(f"   {i}. {poi['name']} - {poi['address']}")
                print(f"      匹配分词: {poi.get('match_segments', [])}")
                print(f"      匹配分数: {poi.get('match_score', 0):.2f}")
        else:
            print("   没有找到完全匹配的POI")
    else:
        print(f"   ❌ 搜索失败: {result2.get('error')}")
    
    print()
    
    # 3. 分词POI搜索（部分匹配即可）
    print("3. 分词POI搜索结果（部分匹配即可）:")
    result3 = mcp_search_poi_with_segmentation(keyword, city, require_all_segments=False)
    
    if result3.get("status"):
        data = result3["data"]
        pois = data["pois"]
        print(f"   过滤后结果数: {data['filtered_count']}")
        
        if pois:
            print("   部分匹配的POI:")
            for i, poi in enumerate(pois[:5], 1):
                print(f"   {i}. {poi['name']} - {poi['address']}")
                print(f"      匹配分词: {poi.get('match_segments', [])}")
                print(f"      匹配分数: {poi.get('match_score', 0):.2f}")
        else:
            print("   没有找到匹配的POI")
    else:
        print(f"   ❌ 搜索失败: {result3.get('error')}")
    
    print("\n" + "="*60 + "\n")

def test_multiple_keywords():
    """测试多个关键词的分词搜索"""
    print("=== 测试多个关键词的分词搜索 ===")
    
    test_cases = [
        ("海淀医院", "北京市"),
        ("中关村软件园", "北京市"),
        ("万达广场", "北京市"),
        ("国贸中心", "北京市"),
        ("清华科技园", "北京市")
    ]
    
    for keyword, city in test_cases:
        print(f"🔍 搜索: {keyword} (在{city})")
        
        result = mcp_search_poi_with_segmentation(keyword, city, require_all_segments=True)
        
        if result.get("status"):
            data = result["data"]
            print(f"   分词: {data['segments']}")
            print(f"   原始结果: {data['original_count']} 个")
            print(f"   过滤结果: {data['filtered_count']} 个")
            
            pois = data["pois"]
            if pois:
                # 只显示最匹配的结果
                best_poi = pois[0]
                print(f"   最佳匹配: {best_poi['name']}")
                print(f"   地址: {best_poi['address']}")
                print(f"   匹配分词: {best_poi.get('match_segments', [])}")
            else:
                print("   ❌ 没有找到完全匹配的结果")
        else:
            print(f"   ❌ 搜索失败: {result.get('error')}")
        
        print()

def test_edge_cases():
    """测试边界情况"""
    print("=== 测试边界情况 ===")
    
    edge_cases = [
        ("A", "北京市"),  # 单字符
        ("ABC大厦", "北京市"),  # 包含英文
        ("123号楼", "北京市"),  # 包含数字
        ("东方明珠", "上海市"),  # 不同城市
        ("不存在的地方", "北京市"),  # 不存在的地点
    ]
    
    for keyword, city in edge_cases:
        print(f"🧪 测试: {keyword} (在{city})")
        
        result = mcp_search_poi_with_segmentation(keyword, city, require_all_segments=True)
        
        if result.get("status"):
            data = result["data"]
            print(f"   分词: {data['segments']}")
            print(f"   过滤结果: {data['filtered_count']} 个")
            
            if data['filtered_count'] > 0:
                best_poi = data["pois"][0]
                print(f"   最佳匹配: {best_poi['name']}")
            else:
                print("   没有找到匹配结果")
        else:
            print(f"   ❌ 搜索失败: {result.get('error')}")
        
        print()

def main():
    """主测试函数"""
    print("🔍 基于分词的POI搜索功能测试")
    print("=" * 60)
    
    # 检查API密钥
    if not os.getenv("AMAP_API_KEY"):
        print("❌ 请设置环境变量 AMAP_API_KEY")
        return
    
    try:
        # 测试分词功能
        test_word_segmentation()
        
        # 对比搜索结果
        test_poi_search_comparison()
        
        # 测试多个关键词
        test_multiple_keywords()
        
        # 测试边界情况
        test_edge_cases()
        
        print("✅ 所有测试完成")
        
    except Exception as e:
        print(f"❌ 测试过程中出现错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
