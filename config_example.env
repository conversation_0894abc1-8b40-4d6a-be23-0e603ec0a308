# 环境变量配置示例
# 复制此文件为 .env 并填入真实的API密钥

# 百炼API密钥（必需）
BAILIAN_API_KEY=your_bailian_api_key_here

# 高德地图API密钥（必需）
AMAP_API_KEY=your_amap_api_key_here

# 默认位置坐标（可选）
# 默认值：116.306345,40.040377（北京市昌平区）
DEFAULT_LONGITUDE=116.306345
DEFAULT_LATITUDE=40.040377

# 是否启用位置上下文（可选）
# 默认值：true
LOCATION_CONTEXT_ENABLED=true

# 使用说明：
# 1. 将此文件重命名为 .env
# 2. 填入真实的API密钥
# 3. 根据需要调整默认坐标
# 4. 运行测试：python test_location_context.py
