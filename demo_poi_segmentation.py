#!/usr/bin/env python3
"""
演示基于分词的POI搜索功能
展示如何通过分词提高POI搜索的精确度
"""

import os
from amap_mcp_tools import AmapMCPTools, mcp_search_poi, mcp_search_poi_with_segmentation

def demo_segmentation_improvement():
    """演示分词搜索的改进效果"""
    print("🎯 基于分词的POI搜索演示")
    print("=" * 60)
    
    # 测试案例
    test_case = {
        "keyword": "方正大厦",
        "city": "北京市"
    }
    
    print(f"🔍 搜索关键词: {test_case['keyword']}")
    print(f"📍 搜索城市: {test_case['city']}")
    print()
    
    # 1. 显示分词结果
    amap = AmapMCPTools()
    segments = amap._simple_word_segment(test_case['keyword'])
    print(f"✂️  分词结果: {test_case['keyword']} → {segments}")
    print()
    
    # 2. 普通搜索
    print("📋 普通POI搜索结果:")
    result1 = mcp_search_poi(test_case['keyword'], test_case['city'])
    
    if result1.get("status"):
        pois = result1["data"]["pois"]
        print(f"   找到 {len(pois)} 个结果（显示前5个）:")
        for i, poi in enumerate(pois[:5], 1):
            print(f"   {i}. {poi['name']}")
            print(f"      地址: {poi['address']}")
    else:
        print(f"   ❌ 搜索失败: {result1.get('error')}")
    
    print()
    
    # 3. 分词搜索（要求所有分词都匹配）
    print("🎯 分词POI搜索结果（精确匹配）:")
    result2 = mcp_search_poi_with_segmentation(test_case['keyword'], test_case['city'], require_all_segments=True)
    
    if result2.get("status"):
        data = result2["data"]
        print(f"   分词要求: 必须包含 {data['segments']} 所有词汇")
        print(f"   原始结果: {data['original_count']} 个")
        print(f"   过滤结果: {data['filtered_count']} 个")
        print()
        
        pois = data["pois"]
        if pois:
            print("   精确匹配的POI:")
            for i, poi in enumerate(pois[:5], 1):
                print(f"   {i}. {poi['name']}")
                print(f"      地址: {poi['address']}")
                print(f"      匹配词汇: {poi.get('match_segments', [])}")
                print(f"      匹配分数: {poi.get('match_score', 0):.2f}")
                print()
        else:
            print("   没有找到完全匹配的POI")
    else:
        print(f"   ❌ 搜索失败: {result2.get('error')}")
    
    print("=" * 60)

def demo_multiple_examples():
    """演示多个分词搜索示例"""
    print("\n🌟 多个分词搜索示例")
    print("=" * 60)
    
    examples = [
        ("海淀医院", "北京市", "医疗机构"),
        ("中关村软件园", "北京市", "科技园区"),
        ("万达广场", "北京市", "商业中心"),
        ("国贸中心", "北京市", "商务区"),
    ]
    
    for keyword, city, category in examples:
        print(f"🏢 {category}: {keyword}")
        
        # 显示分词
        amap = AmapMCPTools()
        segments = amap._simple_word_segment(keyword)
        print(f"   分词: {keyword} → {segments}")
        
        # 分词搜索
        result = mcp_search_poi_with_segmentation(keyword, city, require_all_segments=True)
        
        if result.get("status"):
            data = result["data"]
            print(f"   结果: {data['filtered_count']}/{data['original_count']} 个精确匹配")
            
            if data["pois"]:
                best_poi = data["pois"][0]
                print(f"   最佳匹配: {best_poi['name']}")
                print(f"   地址: {best_poi['address']}")
            else:
                print("   没有找到精确匹配")
        else:
            print(f"   ❌ 搜索失败: {result.get('error')}")
        
        print()

def demo_comparison_modes():
    """演示不同匹配模式的对比"""
    print("🔄 匹配模式对比")
    print("=" * 60)
    
    keyword = "方正大厦"
    city = "北京市"
    
    print(f"🔍 测试关键词: {keyword}")
    
    # 显示分词
    amap = AmapMCPTools()
    segments = amap._simple_word_segment(keyword)
    print(f"📝 分词结果: {segments}")
    print()
    
    # 模式1：要求所有分词都匹配
    print("1️⃣ 严格模式（所有分词都必须匹配）:")
    result1 = mcp_search_poi_with_segmentation(keyword, city, require_all_segments=True)
    
    if result1.get("status"):
        data1 = result1["data"]
        print(f"   过滤结果: {data1['filtered_count']} 个")
        if data1["pois"]:
            print(f"   示例: {data1['pois'][0]['name']}")
    
    # 模式2：部分分词匹配即可
    print("\n2️⃣ 宽松模式（部分分词匹配即可）:")
    result2 = mcp_search_poi_with_segmentation(keyword, city, require_all_segments=False)
    
    if result2.get("status"):
        data2 = result2["data"]
        print(f"   过滤结果: {data2['filtered_count']} 个")
        if data2["pois"]:
            print(f"   示例: {data2['pois'][0]['name']}")
            print(f"   匹配分数: {data2['pois'][0].get('match_score', 0):.2f}")
    
    print()
    print("💡 建议:")
    print("   - 严格模式：适用于需要精确匹配的场景")
    print("   - 宽松模式：适用于模糊搜索或容错性要求高的场景")
    
    print("\n" + "=" * 60)

def demo_practical_usage():
    """演示实际使用场景"""
    print("\n🚀 实际使用场景演示")
    print("=" * 60)
    
    # 模拟用户查询
    user_queries = [
        "我要去方正大厦",
        "帮我找海淀医院",
        "中关村软件园在哪里",
        "万达广场怎么走"
    ]
    
    for query in user_queries:
        print(f"👤 用户: {query}")
        
        # 简单提取关键词（实际应用中可能需要更复杂的NLP处理）
        keywords = ["方正大厦", "海淀医院", "中关村软件园", "万达广场"]
        keyword = None
        for kw in keywords:
            if kw in query:
                keyword = kw
                break
        
        if keyword:
            print(f"🔍 提取关键词: {keyword}")
            
            result = mcp_search_poi_with_segmentation(keyword, "北京市", require_all_segments=True)
            
            if result.get("status") and result["data"]["pois"]:
                poi = result["data"]["pois"][0]
                print(f"🤖 系统: 找到了{poi['name']}，地址是{poi['address']}")
            else:
                print("🤖 系统: 抱歉，没有找到相关地点")
        else:
            print("🤖 系统: 请提供更具体的地点名称")
        
        print()

def main():
    """主演示函数"""
    print("🎯 基于分词的POI搜索功能演示")
    print("🔧 通过智能分词提高搜索精确度")
    print()
    
    # 检查API密钥
    if not os.getenv("AMAP_API_KEY"):
        print("❌ 请先设置环境变量 AMAP_API_KEY")
        print("   export AMAP_API_KEY='your_api_key_here'")
        return
    
    try:
        # 核心功能演示
        demo_segmentation_improvement()
        
        # 多个示例
        demo_multiple_examples()
        
        # 模式对比
        demo_comparison_modes()
        
        # 实际应用
        demo_practical_usage()
        
        print("🎉 演示完成！")
        print("📚 更多信息请查看测试文件: test_poi_segmentation.py")
        
    except Exception as e:
        print(f"❌ 演示过程中出现错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
