#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试新的默认坐标
验证116.306345,40.040377的位置信息
"""

import os
from taxi_agent_system import EnhancedTaxiAgent, MainAgent, DEFAULT_LONGITUDE, DEFAULT_LATITUDE
from amap_mcp_tools import mcp_reverse_geocode_poi


def test_new_default_coordinates():
    """测试新的默认坐标"""
    print("🌍 测试新的默认坐标")
    print("=" * 50)
    
    print(f"新的默认坐标: {DEFAULT_LONGITUDE}, {DEFAULT_LATITUDE}")
    print("预期位置: 北京市昌平区")
    
    # 检查环境变量
    if not os.getenv("AMAP_API_KEY"):
        print("⚠️  警告: 未设置AMAP_API_KEY，无法获取位置信息")
        return False
    
    try:
        # 直接测试坐标转换
        print(f"\n🔍 查询坐标 {DEFAULT_LONGITUDE}, {DEFAULT_LATITUDE} 的位置信息...")
        
        result = mcp_reverse_geocode_poi(DEFAULT_LONGITUDE, DEFAULT_LATITUDE, 1000)
        
        if result.get("status"):
            data = result.get("data", {})
            formatted_address = data.get("formatted_address", "")
            nearby_pois = data.get("nearby_pois", [])
            
            print(f"✅ 位置查询成功")
            print(f"📍 地址: {formatted_address}")
            print(f"🏢 附近POI数量: {len(nearby_pois)}")
            
            if nearby_pois:
                print(f"\n📋 附近POI列表 (前5个):")
                for i, poi in enumerate(nearby_pois[:5], 1):
                    name = poi.get("name", "")
                    poi_type = poi.get("type", "")
                    distance = poi.get("distance", 0)
                    print(f"  {i}. {name} ({poi_type}, {distance}米)")
            else:
                print("📋 附近暂无POI信息")
            
            return True
        else:
            print(f"❌ 位置查询失败: {result.get('error', '未知错误')}")
            return False
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False


def test_agent_with_new_coordinates():
    """测试Agent使用新坐标的效果"""
    print("\n\n🤖 测试Agent使用新坐标")
    print("=" * 50)
    
    try:
        # 初始化Agent
        agent = EnhancedTaxiAgent()
        
        # 检查位置上下文
        location_context = agent.get_location_context()
        print(f"Agent默认坐标: ({location_context['default_longitude']}, {location_context['default_latitude']})")
        
        if location_context.get("current_location_info"):
            info = location_context["current_location_info"]
            print(f"当前位置: {info.get('formatted_address', '未知')}")
            
            nearby_pois = info.get("nearby_pois", [])
            if nearby_pois:
                print(f"环境上下文POI:")
                for poi in nearby_pois:
                    name = poi.get("name", "")
                    distance = poi.get("distance", 0)
                    print(f"  - {name} ({distance}米)")
        else:
            print("位置上下文: 未初始化")
        
        # 测试位置感知对话
        print(f"\n💬 测试位置感知对话:")
        test_inputs = [
            "我在哪里？",
            "附近有什么？",
            "从这里到天安门怎么走？"
        ]
        
        for i, test_input in enumerate(test_inputs, 1):
            print(f"\n--- 测试 {i}: {test_input} ---")
            try:
                if os.getenv("BAILIAN_API_KEY"):
                    response = agent.process_message(test_input, f"coord_test_{i}")
                    print(f"回复: {response}")
                else:
                    print("⚠️  跳过对话测试 (未设置BAILIAN_API_KEY)")
            except Exception as e:
                print(f"对话测试失败: {e}")
        
        return True
        
    except Exception as e:
        print(f"❌ Agent测试失败: {e}")
        return False


def test_coordinate_comparison():
    """对比新旧坐标的差异"""
    print("\n\n📊 新旧坐标对比")
    print("=" * 50)
    
    old_coords = (116.316666, 39.990308)  # 海淀区
    new_coords = (116.306345, 40.040377)  # 昌平区
    
    print(f"旧坐标: {old_coords[0]}, {old_coords[1]} (海淀区)")
    print(f"新坐标: {new_coords[0]}, {new_coords[1]} (昌平区)")
    
    # 计算距离差异
    import math
    
    def calculate_distance(lat1, lon1, lat2, lon2):
        """计算两点间距离（公里）"""
        R = 6371  # 地球半径
        
        lat1_rad = math.radians(lat1)
        lon1_rad = math.radians(lon1)
        lat2_rad = math.radians(lat2)
        lon2_rad = math.radians(lon2)
        
        dlat = lat2_rad - lat1_rad
        dlon = lon2_rad - lon1_rad
        
        a = math.sin(dlat/2)**2 + math.cos(lat1_rad) * math.cos(lat2_rad) * math.sin(dlon/2)**2
        c = 2 * math.atan2(math.sqrt(a), math.sqrt(1-a))
        
        return R * c
    
    distance = calculate_distance(old_coords[1], old_coords[0], new_coords[1], new_coords[0])
    print(f"📏 坐标间距离: {distance:.2f} 公里")
    
    # 如果有API密钥，获取两个位置的详细信息
    if os.getenv("AMAP_API_KEY"):
        try:
            print(f"\n🔍 获取两个位置的详细信息...")
            
            # 旧坐标信息
            old_result = mcp_reverse_geocode_poi(old_coords[0], old_coords[1], 1000)
            if old_result.get("status"):
                old_address = old_result.get("data", {}).get("formatted_address", "")
                print(f"旧位置: {old_address}")
            
            # 新坐标信息
            new_result = mcp_reverse_geocode_poi(new_coords[0], new_coords[1], 1000)
            if new_result.get("status"):
                new_address = new_result.get("data", {}).get("formatted_address", "")
                print(f"新位置: {new_address}")
                
        except Exception as e:
            print(f"位置信息获取失败: {e}")
    
    return True


def main():
    """主测试函数"""
    print("🚗 新默认坐标测试")
    print("坐标已更改为: 116.306345, 40.040377")
    print("=" * 60)
    
    # 检查环境
    if not os.getenv("AMAP_API_KEY"):
        print("⚠️  警告: 未设置AMAP_API_KEY，部分功能无法测试")
    
    if not os.getenv("BAILIAN_API_KEY"):
        print("⚠️  警告: 未设置BAILIAN_API_KEY，对话功能无法测试")
    
    results = []
    
    # 1. 测试新坐标
    results.append(("新坐标位置查询", test_new_default_coordinates()))
    
    # 2. 测试Agent集成
    results.append(("Agent位置上下文", test_agent_with_new_coordinates()))
    
    # 3. 坐标对比
    results.append(("新旧坐标对比", test_coordinate_comparison()))
    
    # 汇总结果
    print("\n" + "=" * 60)
    print("测试结果汇总:")
    
    passed = 0
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"  {test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\n总体结果: {passed}/{len(results)} 测试通过")
    
    if passed == len(results):
        print("🎉 新坐标设置成功！")
        print(f"\n📍 系统现在默认位置为:")
        print(f"   经度: {DEFAULT_LONGITUDE}")
        print(f"   纬度: {DEFAULT_LATITUDE}")
        print(f"   区域: 北京市昌平区")
    else:
        print("⚠️  部分测试失败，请检查配置。")
    
    return passed == len(results)


if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
