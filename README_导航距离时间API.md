# 高德地图导航距离和预估时间API

基于高德地图MCP服务，提供两个核心功能：
1. **经纬度到经纬度**的导航距离和时间计算
2. **标准POI名称到标准POI名称**的导航距离和时间计算

## 功能特性

### 1. 经纬度导航计算 (`mcp_calculate_driving_route`)
- 输入起点和终点的经纬度坐标
- 返回驾车距离、预估时间、过路费、红绿灯数量等详细信息
- 支持多种路径规划策略（躲避拥堵、不走高速、避免收费等）

### 2. POI名称导航计算 (`mcp_calculate_poi_to_poi_route`)
- 输入起点和终点的POI名称（如"北京天安门"、"北京西站"）
- 自动进行地址解析，转换为坐标后计算路径
- 返回完整的地址信息和导航数据

## API接口说明

### 经纬度导航计算

```python
from amap_mcp_tools import mcp_calculate_driving_route

result = mcp_calculate_driving_route(
    origin_lng=116.397428,    # 起点经度
    origin_lat=39.90923,      # 起点纬度
    dest_lng=116.322056,      # 终点经度
    dest_lat=39.893729,       # 终点纬度
    strategy=10               # 路径策略（可选，默认10）
)
```

**返回数据格式：**
```json
{
    "status": true,
    "data": {
        "distance": 8420,           // 距离（米）
        "duration": 1680,           // 时间（秒）
        "tolls": 0.0,              // 过路费（元）
        "traffic_lights": 12,       // 红绿灯数量
        "restriction": 0,           // 限行状态（0:无限行 1:有限行）
        "origin": "116.397428,39.90923",
        "destination": "116.322056,39.893729",
        "strategy": 10
    }
}
```

### POI名称导航计算

```python
from amap_mcp_tools import mcp_calculate_poi_to_poi_route

result = mcp_calculate_poi_to_poi_route(
    origin_poi="北京天安门",      # 起点POI名称
    dest_poi="北京西站",          # 终点POI名称
    origin_city="北京",           # 起点城市（可选）
    dest_city="北京",             # 终点城市（可选）
    strategy=10                   # 路径策略（可选）
)
```

**返回数据格式：**
```json
{
    "status": true,
    "data": {
        "distance": 8420,
        "duration": 1680,
        "tolls": 0.0,
        "traffic_lights": 12,
        "restriction": 0,
        "origin_poi": "北京天安门",
        "destination_poi": "北京西站",
        "origin_address": "北京市东城区东长安街天安门广场",
        "destination_address": "北京市丰台区莲花池东路118号",
        "origin_coords": "116.397428,39.90923",
        "destination_coords": "116.322056,39.893729",
        "strategy": 10
    }
}
```

## 路径规划策略

| 策略代码 | 说明 |
|---------|------|
| 10 | 躲避拥堵，路程较短（推荐默认策略） |
| 11 | 返回三个结果：时间最短、距离最短、躲避拥堵 |
| 12 | 躲避拥堵策略 |
| 13 | 不走高速策略 |
| 14 | 避免收费策略 |
| 15 | 躲避拥堵&不走高速 |
| 16 | 避免收费&不走高速 |
| 17 | 躲避拥堵&避免收费 |
| 18 | 躲避拥堵&避免收费&不走高速 |
| 19 | 高速优先策略 |
| 20 | 躲避拥堵&高速优先 |

## 在taxi_agent_system中的集成

新的导航API已经集成到`EnhancedTaxiAgent`中，支持以下用户查询：

```python
from taxi_agent_system import EnhancedTaxiAgent

agent = EnhancedTaxiAgent()

# 用户可以这样询问
responses = [
    agent.process_message("从北京天安门到北京西站开车需要多长时间？"),
    agent.process_message("计算一下从经纬度116.397428,39.90923到116.322056,39.893729的驾车距离"),
    agent.process_message("我想知道从杭州西湖到杭州东站的路程和时间")
]
```

## 使用示例

### 基础使用
```python
# 1. 经纬度计算
result = mcp_calculate_driving_route(116.397428, 39.90923, 116.322056, 39.893729)
if result["status"]:
    data = result["data"]
    print(f"距离: {data['distance']}米")
    print(f"时间: {data['duration']//60}分{data['duration']%60}秒")

# 2. POI名称计算
result = mcp_calculate_poi_to_poi_route("北京天安门", "北京西站")
if result["status"]:
    data = result["data"]
    print(f"从{data['origin_poi']}到{data['destination_poi']}")
    print(f"距离: {data['distance']/1000:.1f}公里")
    print(f"时间: {data['duration']//60}分钟")
```

### 不同策略对比
```python
strategies = [10, 12, 13, 14, 19]  # 不同策略
for strategy in strategies:
    result = mcp_calculate_driving_route(116.397428, 39.90923, 116.322056, 39.893729, strategy)
    if result["status"]:
        data = result["data"]
        print(f"策略{strategy}: {data['distance']}米, {data['duration']//60}分钟, {data['tolls']}元")
```

## 环境配置

确保设置了高德地图API密钥：
```bash
export AMAP_API_KEY="your_amap_api_key_here"
```

## 测试

运行测试文件验证功能：
```bash
python test_navigation_distance.py
```

## 注意事项

1. **API限制**: 遵循高德地图API的调用频率限制
2. **坐标系**: 使用GCJ-02坐标系（高德坐标系）
3. **精度**: 经纬度小数点后不超过6位
4. **实时性**: 路径规划会考虑实时路况，不同时间查询可能返回不同结果
5. **距离限制**: 支持长距离跨城路径规划

## 错误处理

所有API都返回统一的错误格式：
```json
{
    "status": false,
    "error": "具体错误信息"
}
```

常见错误：
- API密钥无效或未设置
- 网络连接问题
- 坐标格式错误
- POI名称无法解析
- 无法找到可行路径

## 更新日志

- **v1.0**: 初始版本，支持基础的经纬度和POI导航计算
- 集成到taxi_agent_system，支持自然语言查询
- 支持多种路径规划策略
- 完整的错误处理和测试覆盖
