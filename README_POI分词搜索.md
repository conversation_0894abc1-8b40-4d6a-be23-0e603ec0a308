# POI分词搜索功能

基于智能分词的POI搜索功能，通过对搜索关键词进行分词处理，要求分词结果都在POI名称中出现，从而提供更精确的搜索结果。

## 功能概述

### 问题背景
在原始的POI搜索中，搜索"方正大厦"可能会返回很多不相关的结果，比如"北大科技大厦"等。用户希望搜索结果更精确，只返回真正包含"方正"和"大厦"这两个关键词的POI。

### 解决方案
通过智能分词技术，将"方正大厦"分解为"方正"+"大厦"，然后要求搜索结果的POI名称必须同时包含这两个词汇，从而过滤掉不相关的结果。

## 核心功能

### 1. 智能分词 (`_simple_word_segment`)
- 基于常见词汇库进行分词
- 识别建筑物后缀词：大厦、大楼、广场、中心、医院、学校等
- 识别机构名称：方正、联想、百度、中关村、海淀等
- 处理字母数字组合和方位词

### 2. 分词POI搜索 (`search_poi_with_segmentation`)
- 对搜索关键词进行智能分词
- 根据分词结果过滤POI搜索结果
- 支持严格匹配和宽松匹配两种模式
- 提供匹配分数和匹配词汇信息

## API接口

### 基础分词搜索
```python
from amap_mcp_tools import mcp_search_poi_with_segmentation

result = mcp_search_poi_with_segmentation(
    keyword="方正大厦",           # 搜索关键词
    city="北京市",               # 城市名称（可选）
    require_all_segments=True    # 是否要求所有分词都匹配
)
```

### 返回数据格式
```json
{
    "status": true,
    "data": {
        "original_keyword": "方正大厦",
        "segments": ["方正", "大厦"],
        "require_all_segments": true,
        "original_count": 20,
        "filtered_count": 13,
        "pois": [
            {
                "name": "方正国际大厦",
                "address": "北四环西路52号",
                "match_segments": ["方正", "大厦"],
                "match_score": 1.0,
                "longitude": 116.xxx,
                "latitude": 39.xxx,
                "type": "商务住宅",
                "tel": "010-xxxxxxxx"
            }
        ]
    }
}
```

## 分词示例

| 原始关键词 | 分词结果 | 说明 |
|-----------|----------|------|
| 方正大厦 | ['方正', '大厦'] | 机构名+建筑后缀 |
| 海淀医院 | ['海淀', '医院'] | 地区名+建筑类型 |
| 中关村软件园 | ['中关村', '软件园'] | 地区名+园区类型 |
| 万达广场 | ['万达', '广场'] | 品牌名+建筑类型 |
| 北京大学 | ['北京大学'] | 完整机构名 |
| 清华科技园 | ['清华科技园'] | 完整园区名 |

## 匹配模式

### 严格模式 (`require_all_segments=True`)
- 要求所有分词都在POI名称中出现
- 适用于需要精确匹配的场景
- 过滤效果更强，结果更精确

### 宽松模式 (`require_all_segments=False`)
- 只要有部分分词匹配即可
- 适用于模糊搜索或容错性要求高的场景
- 返回更多候选结果，按匹配分数排序

## 使用示例

### 基础使用
```python
from amap_mcp_tools import AmapMCPTools, mcp_search_poi_with_segmentation

# 1. 查看分词结果
amap = AmapMCPTools()
segments = amap._simple_word_segment("方正大厦")
print(f"分词结果: {segments}")  # ['方正', '大厦']

# 2. 分词搜索
result = mcp_search_poi_with_segmentation("方正大厦", "北京市")
if result["status"]:
    data = result["data"]
    print(f"原始结果: {data['original_count']} 个")
    print(f"过滤结果: {data['filtered_count']} 个")
    
    for poi in data["pois"][:3]:
        print(f"- {poi['name']}: {poi['address']}")
        print(f"  匹配词汇: {poi['match_segments']}")
```

### 对比普通搜索和分词搜索
```python
# 普通搜索
normal_result = mcp_search_poi("方正大厦", "北京市")
print(f"普通搜索: {len(normal_result['data']['pois'])} 个结果")

# 分词搜索
segment_result = mcp_search_poi_with_segmentation("方正大厦", "北京市")
print(f"分词搜索: {segment_result['data']['filtered_count']} 个精确结果")
```

## 实际效果对比

### 搜索"方正大厦"

**普通搜索结果（前5个）：**
1. 北大科技大厦 ❌ (不相关)
2. 方正国际大厦 ✅ 
3. 方正大厦(上地五街) ✅
4. 方正大厦(南门) ✅
5. 方正证券大厦 ✅

**分词搜索结果（严格模式）：**
- 原始结果：20个
- 过滤结果：13个
- 所有结果都包含"方正"和"大厦"两个关键词

### 搜索精确度提升
- **海淀医院**: 20/20 个精确匹配
- **中关村软件园**: 19/20 个精确匹配  
- **万达广场**: 20/20 个精确匹配
- **国贸中心**: 9/20 个精确匹配

## 集成到taxi_agent_system

新的分词搜索功能已经集成到MCP工具中，可以通过以下方式使用：

```python
# 在AMAP_TOOLS中已添加新工具定义
{
    "type": "function",
    "function": {
        "name": "mcp_search_poi_with_segmentation",
        "description": "基于分词的POI搜索。先对关键词进行分词，然后要求分词结果都在POI名称中出现。",
        "parameters": {
            "type": "object",
            "properties": {
                "keyword": {"type": "string", "description": "搜索关键词"},
                "city": {"type": "string", "description": "城市名称"},
                "require_all_segments": {"type": "boolean", "description": "是否要求所有分词都匹配"}
            },
            "required": ["keyword"]
        }
    }
}
```

## 技术特点

### 1. 智能分词算法
- 基于词汇库的最长匹配
- 优先识别完整的机构名称
- 正确处理复合词和专有名词

### 2. 灵活的匹配策略
- 支持严格匹配和宽松匹配
- 提供匹配分数用于结果排序
- 保留原始搜索结果用于对比

### 3. 完善的错误处理
- 统一的错误返回格式
- 详细的调试信息
- 网络异常处理

## 测试和验证

### 运行测试
```bash
# 完整测试套件
python test_poi_segmentation.py

# 功能演示
python demo_poi_segmentation.py
```

### 测试覆盖
- ✅ 基础分词功能测试
- ✅ 普通搜索vs分词搜索对比
- ✅ 多种关键词测试
- ✅ 边界情况测试
- ✅ 不同匹配模式测试

## 应用场景

1. **精确地点搜索**: 用户搜索特定建筑物时获得更准确的结果
2. **智能导航**: 减少歧义，提高导航准确性
3. **POI推荐**: 基于用户意图提供更相关的推荐
4. **语音搜索**: 提高语音识别后的搜索精度
5. **企业应用**: 内部地点管理和查询系统

## 总结

通过引入智能分词技术，POI搜索的精确度得到显著提升：

- **搜索精度**: 从模糊匹配提升到精确匹配
- **结果质量**: 过滤掉不相关结果，提高相关性
- **用户体验**: 更快找到目标地点，减少选择困扰
- **系统智能**: 更好理解用户搜索意图

这个功能为整个出行服务系统提供了更强大的地点搜索能力，是提升用户体验的重要改进。
