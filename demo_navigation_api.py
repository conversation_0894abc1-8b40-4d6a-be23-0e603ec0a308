#!/usr/bin/env python3
"""
高德地图导航距离和预估时间API演示
展示两个核心功能的使用方法
"""

import os
from amap_mcp_tools import mcp_calculate_driving_route, mcp_calculate_poi_to_poi_route
from taxi_agent_system import EnhancedTaxiAgent

def demo_coordinate_navigation():
    """演示经纬度导航计算"""
    print("🚗 经纬度导航计算演示")
    print("=" * 50)
    
    # 北京天安门到北京西站
    origin_lng, origin_lat = 116.397428, 39.90923    # 天安门
    dest_lng, dest_lat = 116.322056, 39.893729       # 北京西站
    
    print(f"📍 起点: 经度{origin_lng}, 纬度{origin_lat} (天安门)")
    print(f"📍 终点: 经度{dest_lng}, 纬度{dest_lat} (北京西站)")
    print()
    
    result = mcp_calculate_driving_route(origin_lng, origin_lat, dest_lng, dest_lat)
    
    if result.get("status"):
        data = result["data"]
        distance_km = data['distance'] / 1000
        duration_min = data['duration'] // 60
        duration_sec = data['duration'] % 60
        
        print("✅ 路径规划成功！")
        print(f"🛣️  距离: {distance_km:.1f}公里")
        print(f"⏱️  时间: {duration_min}分{duration_sec}秒")
        print(f"💰 过路费: {data['tolls']}元")
        print(f"🚦 红绿灯: {data['traffic_lights']}个")
        print(f"🚫 限行: {'有限行' if data['restriction'] else '无限行'}")
    else:
        print(f"❌ 计算失败: {result.get('error')}")
    
    print("\n" + "="*50 + "\n")

def demo_poi_navigation():
    """演示POI名称导航计算"""
    print("🏢 POI名称导航计算演示")
    print("=" * 50)
    
    origin_poi = "杭州西湖"
    dest_poi = "杭州东站"
    
    print(f"🏞️  起点: {origin_poi}")
    print(f"🚄 终点: {dest_poi}")
    print()
    
    result = mcp_calculate_poi_to_poi_route(origin_poi, dest_poi)
    
    if result.get("status"):
        data = result["data"]
        distance_km = data['distance'] / 1000
        duration_min = data['duration'] // 60
        
        print("✅ POI路径规划成功！")
        print(f"📍 起点地址: {data['origin_address']}")
        print(f"📍 终点地址: {data['destination_address']}")
        print(f"🗺️  坐标: {data['origin_coords']} → {data['destination_coords']}")
        print(f"🛣️  距离: {distance_km:.1f}公里")
        print(f"⏱️  时间: {duration_min}分钟")
        print(f"💰 过路费: {data['tolls']}元")
        print(f"🚦 红绿灯: {data['traffic_lights']}个")
    else:
        print(f"❌ 计算失败: {result.get('error')}")
    
    print("\n" + "="*50 + "\n")

def demo_strategy_comparison():
    """演示不同策略的对比"""
    print("🔄 路径策略对比演示")
    print("=" * 50)
    
    origin_lng, origin_lat = 116.397428, 39.90923
    dest_lng, dest_lat = 116.322056, 39.893729
    
    strategies = {
        10: "躲避拥堵，路程较短（推荐）",
        12: "躲避拥堵",
        13: "不走高速",
        14: "避免收费",
        19: "高速优先"
    }
    
    print("📊 同一路线，不同策略对比：")
    print()
    
    for strategy, description in strategies.items():
        result = mcp_calculate_driving_route(origin_lng, origin_lat, dest_lng, dest_lat, strategy)
        
        if result.get("status"):
            data = result["data"]
            distance_km = data['distance'] / 1000
            duration_min = data['duration'] // 60
            
            print(f"策略 {strategy:2d}: {description}")
            print(f"         距离: {distance_km:.1f}km, 时间: {duration_min}分钟, 费用: {data['tolls']}元")
        else:
            print(f"策略 {strategy:2d}: ❌ 失败")
        print()
    
    print("="*50 + "\n")

def demo_agent_integration():
    """演示与智能助手的集成"""
    print("🤖 智能助手集成演示")
    print("=" * 50)
    
    try:
        agent = EnhancedTaxiAgent()
        
        queries = [
            "从北京天安门到北京西站开车要多久？",
            "计算从上海外滩到上海虹桥机场的距离",
            "我想知道从广州塔到广州南站的路程"
        ]
        
        for i, query in enumerate(queries, 1):
            print(f"💬 用户问题 {i}: {query}")
            response = agent.process_message(query)
            print(f"🤖 助手回复: {response}")
            print()
            
    except Exception as e:
        print(f"❌ 集成演示失败: {e}")
    
    print("="*50 + "\n")

def demo_cross_city():
    """演示跨城市导航"""
    print("🌏 跨城市导航演示")
    print("=" * 50)
    
    origin_poi = "北京天安门"
    dest_poi = "上海外滩"
    
    print(f"🏛️  起点: {origin_poi}")
    print(f"🌃 终点: {dest_poi}")
    print("🚗 计算跨城驾车路线...")
    print()
    
    result = mcp_calculate_poi_to_poi_route(origin_poi, dest_poi, "北京", "上海")
    
    if result.get("status"):
        data = result["data"]
        distance_km = data['distance'] / 1000
        duration_hours = data['duration'] // 3600
        duration_min = (data['duration'] % 3600) // 60
        
        print("✅ 跨城路径规划成功！")
        print(f"🛣️  总距离: {distance_km:.0f}公里")
        print(f"⏱️  预计时间: {duration_hours}小时{duration_min}分钟")
        print(f"💰 过路费: {data['tolls']}元")
        print(f"📍 详细路线: {data['origin_address']} → {data['destination_address']}")
    else:
        print(f"❌ 跨城计算失败: {result.get('error')}")
    
    print("\n" + "="*50 + "\n")

def main():
    """主演示函数"""
    print("🎯 高德地图导航距离和预估时间API演示")
    print("🔧 基于高德MCP服务，提供精确的导航计算")
    print("=" * 60)
    print()
    
    # 检查API密钥
    if not os.getenv("AMAP_API_KEY"):
        print("❌ 请先设置环境变量 AMAP_API_KEY")
        print("   export AMAP_API_KEY='your_api_key_here'")
        return
    
    try:
        # 基础功能演示
        demo_coordinate_navigation()
        demo_poi_navigation()
        
        # 高级功能演示
        demo_strategy_comparison()
        demo_cross_city()
        
        # 集成演示
        demo_agent_integration()
        
        print("🎉 演示完成！")
        print("📚 更多信息请查看 README_导航距离时间API.md")
        
    except Exception as e:
        print(f"❌ 演示过程中出现错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
