#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试function calling修复
验证400错误是否已解决
"""

import os
from taxi_agent_system import MainAgent, EnhancedTaxiAgent


def test_function_calling_fix():
    """测试function calling修复"""
    print("🔧 测试function calling修复")
    print("=" * 50)
    
    # 检查环境变量
    if not os.getenv("BAILIAN_API_KEY"):
        print("⚠️  警告: 未设置BAILIAN_API_KEY，无法测试")
        return False
    
    if not os.getenv("AMAP_API_KEY"):
        print("⚠️  警告: 未设置AMAP_API_KEY，地图功能可能受限")
    
    try:
        # 使用MainAgent测试
        print("\n1. 测试MainAgent...")
        main_agent = MainAgent()
        
        # 测试用例
        test_cases = [
            "从这里打车到机场",
            "我要从康德大厦打车到太阳宫",
            "附近有什么好吃的？",
            "经纬度116.397428,39.90923附近有什么？"
        ]
        
        for i, test_input in enumerate(test_cases, 1):
            print(f"\n--- 测试 {i}: {test_input} ---")
            try:
                response = main_agent.process_user_input(test_input)
                print(f"✅ 成功: {response}")
            except Exception as e:
                print(f"❌ 失败: {e}")
                if "400" in str(e) and "tool_calls" in str(e):
                    print("🚨 检测到function calling 400错误！")
                    return False
        
        print("\n2. 测试EnhancedTaxiAgent...")
        enhanced_agent = EnhancedTaxiAgent()
        
        for i, test_input in enumerate(test_cases, 1):
            print(f"\n--- Enhanced测试 {i}: {test_input} ---")
            try:
                response = enhanced_agent.process_message(test_input, f"test_session_{i}")
                print(f"✅ 成功: {response}")
            except Exception as e:
                print(f"❌ 失败: {e}")
                if "400" in str(e) and "tool_calls" in str(e):
                    print("🚨 检测到function calling 400错误！")
                    return False
        
        print("\n🎉 所有测试通过，function calling修复成功！")
        return True
        
    except Exception as e:
        print(f"❌ 测试过程中出错: {e}")
        return False


def test_location_context():
    """测试位置上下文功能"""
    print("\n\n🌍 测试位置上下文功能")
    print("=" * 50)
    
    try:
        agent = EnhancedTaxiAgent()
        
        # 检查位置上下文
        location_context = agent.get_location_context()
        print(f"默认坐标: ({location_context['default_longitude']}, {location_context['default_latitude']})")
        
        if location_context.get("current_location_info"):
            info = location_context["current_location_info"]
            print(f"当前位置: {info.get('formatted_address', '未知')}")
            
            nearby_pois = info.get("nearby_pois", [])
            if nearby_pois:
                print(f"附近POI: {len(nearby_pois)}个")
                for poi in nearby_pois[:3]:
                    print(f"  - {poi.get('name', '')} ({poi.get('distance', 0)}米)")
        else:
            print("位置上下文: 未初始化")
        
        return True
        
    except Exception as e:
        print(f"❌ 位置上下文测试失败: {e}")
        return False


def test_new_tools():
    """测试新增工具"""
    print("\n\n🔧 测试新增工具")
    print("=" * 50)
    
    try:
        from amap_mcp_tools import mcp_reverse_geocode_poi, mcp_recommend_similar_poi
        
        # 测试经纬度转POI
        print("1. 测试经纬度转POI...")
        result1 = mcp_reverse_geocode_poi(116.397428, 39.90923)
        if result1.get("status"):
            print("✅ 经纬度转POI成功")
            data = result1.get("data", {})
            print(f"   位置: {data.get('formatted_address', '')}")
            pois = data.get("nearby_pois", [])
            print(f"   附近POI: {len(pois)}个")
        else:
            print(f"❌ 经纬度转POI失败: {result1.get('error', '')}")
        
        # 测试POI推荐
        print("\n2. 测试POI推荐...")
        result2 = mcp_recommend_similar_poi("北京上地星巴克")
        if result2.get("status"):
            print("✅ POI推荐成功")
            data = result2.get("data", {})
            original = data.get("original_poi", {})
            print(f"   原始POI: {original.get('name', '')}")
            similar = data.get("similar_pois", [])
            print(f"   推荐POI: {len(similar)}个")
        else:
            print(f"❌ POI推荐失败: {result2.get('error', '')}")
        
        return True
        
    except Exception as e:
        print(f"❌ 新工具测试失败: {e}")
        return False


def main():
    """主测试函数"""
    print("🚗 Function Calling修复验证")
    print("解决400错误: tool_calls must be followed by tool messages")
    print("=" * 60)
    
    results = []
    
    # 1. 测试function calling修复
    results.append(("Function Calling修复", test_function_calling_fix()))
    
    # 2. 测试位置上下文
    results.append(("位置上下文功能", test_location_context()))
    
    # 3. 测试新工具
    results.append(("新增工具功能", test_new_tools()))
    
    # 汇总结果
    print("\n" + "=" * 60)
    print("测试结果汇总:")
    
    passed = 0
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"  {test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\n总体结果: {passed}/{len(results)} 测试通过")
    
    if passed == len(results):
        print("🎉 所有测试通过！系统修复成功。")
        print("\n现在可以正常使用:")
        print("- agent.process_user_input('从这里打车到机场')")
        print("- agent.process_user_input('附近有什么好吃的？')")
    else:
        print("⚠️  部分测试失败，请检查配置。")
    
    return passed == len(results)


if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
