# 位置上下文功能说明

## 🎯 功能概述

在`taxi_agent_system.py`中新增了位置上下文功能，通过默认经纬度自动获取环境信息，并将其集成到大模型的prompt中，提供更智能的位置感知服务。

## 🔧 核心功能

### 1. 环境变量配置

```python
# 默认经纬度配置
DEFAULT_LONGITUDE = float(os.getenv("DEFAULT_LONGITUDE", "116.306345"))  # 默认经度
DEFAULT_LATITUDE = float(os.getenv("DEFAULT_LATITUDE", "40.040377"))     # 默认纬度
LOCATION_CONTEXT_ENABLED = os.getenv("LOCATION_CONTEXT_ENABLED", "true").lower() == "true"
```

**默认坐标**: 116.306345, 40.040377（北京市昌平区）

### 2. 新增工具

#### mcp_reverse_geocode_poi
- **功能**: 经纬度转POI
- **输入**: 经度、纬度、搜索半径
- **输出**: 附近的POI列表（商店、餐厅、景点等）

#### mcp_recommend_similar_poi  
- **功能**: POI推荐
- **输入**: 模糊POI名称（如"北京上地星巴克"）
- **输出**: 附近相似的POI（如上地星巴克附近的咖啡店）

### 3. 位置上下文管理

#### 自动初始化
```python
def _initialize_location_context(self):
    """初始化位置上下文信息"""
    # 使用默认经纬度获取位置信息
    # 更新系统提示词，加入位置上下文
```

#### 动态更新
```python
def update_location_context(self, longitude: float, latitude: float):
    """更新位置上下文"""
    # 获取新位置信息
    # 更新系统提示词
```

### 4. 智能提示词集成

系统会自动将位置信息集成到prompt中：

```
当前环境信息：
- 所在位置：北京市昌平区
- 坐标：经度116.306345, 纬度40.040377
- 附近地标：昌平区政府(政府机构, 500米), 昌平线地铁站(地铁站, 800米), 昌平商场(商场, 1000米)

在处理用户请求时，可以参考当前位置信息提供更准确的服务。
```

## 📋 使用方法

### 1. 环境配置

```bash
# 设置API密钥
export BAILIAN_API_KEY="your_bailian_api_key"
export AMAP_API_KEY="your_amap_api_key"

# 设置默认位置（可选）
export DEFAULT_LONGITUDE="116.306345"
export DEFAULT_LATITUDE="40.040377"

# 启用位置上下文（可选）
export LOCATION_CONTEXT_ENABLED="true"
```

### 2. 代码使用

```python
from taxi_agent_system import EnhancedTaxiAgent, MainAgent

# 初始化（自动加载位置上下文）
agent = EnhancedTaxiAgent()

# 查看位置上下文
location_context = agent.get_location_context()
print(location_context)

# 更新位置
agent.update_location_context(116.397428, 39.90923)  # 天安门坐标

# 使用新工具
response = agent.process_message("经纬度116.397428,39.90923附近有什么？")
response = agent.process_message("推荐北京上地星巴克附近的咖啡店")
```

### 3. MainAgent兼容

```python
# MainAgent自动继承位置上下文功能
main_agent = MainAgent()
response = main_agent.process_user_input("我在哪里？")
```

## 🔍 工具详细说明

### mcp_reverse_geocode_poi

**Function Calling定义**:
```json
{
  "name": "mcp_reverse_geocode_poi",
  "description": "根据经纬度坐标查找附近的POI（兴趣点）",
  "parameters": {
    "longitude": {"type": "number", "description": "经度"},
    "latitude": {"type": "number", "description": "纬度"}, 
    "radius": {"type": "integer", "description": "搜索半径（米）"}
  },
  "required": ["longitude", "latitude"]
}
```

**返回格式**:
```json
{
  "status": true,
  "data": {
    "location": {"longitude": 116.316666, "latitude": 39.990308},
    "formatted_address": "北京市海淀区中关村街道",
    "nearby_pois": [
      {
        "name": "中关村大街",
        "address": "北京市海淀区",
        "type": "道路",
        "distance": 100,
        "longitude": 116.316766,
        "latitude": 39.990408
      }
    ],
    "total_count": 20
  }
}
```

### mcp_recommend_similar_poi

**Function Calling定义**:
```json
{
  "name": "mcp_recommend_similar_poi", 
  "description": "根据POI名称推荐附近相似的POI",
  "parameters": {
    "poi_name": {"type": "string", "description": "POI名称"},
    "city": {"type": "string", "description": "城市名称"},
    "radius": {"type": "integer", "description": "推荐范围半径"}
  },
  "required": ["poi_name"]
}
```

**返回格式**:
```json
{
  "status": true,
  "data": {
    "original_poi": {
      "name": "星巴克(上地店)",
      "longitude": 116.316666,
      "latitude": 39.990308
    },
    "similar_pois": [
      {
        "name": "Costa咖啡",
        "similarity_score": 0.85,
        "distance": 150
      }
    ],
    "total_count": 5
  }
}
```

## 🧪 测试验证

运行测试脚本：
```bash
python test_location_context.py
```

测试内容：
1. ✅ 位置上下文初始化
2. ✅ 对话中的位置感知
3. ✅ 位置更新功能  
4. ✅ 新工具功能测试
5. ✅ MainAgent兼容性

## 📝 配置建议

### 1. 生产环境
```bash
# 使用真实的服务位置
export DEFAULT_LONGITUDE="116.397428"  # 天安门
export DEFAULT_LATITUDE="39.90923"
export LOCATION_CONTEXT_ENABLED="true"
```

### 2. 开发环境
```bash
# 使用测试位置
export DEFAULT_LONGITUDE="116.316666"  # 中关村
export DEFAULT_LATITUDE="39.990308"
export LOCATION_CONTEXT_ENABLED="true"
```

### 3. 禁用位置上下文
```bash
export LOCATION_CONTEXT_ENABLED="false"
```

## 🎯 应用场景

### 1. 智能推荐
- "附近有什么好吃的？" → 基于当前位置推荐餐厅
- "我要去最近的商场" → 自动识别附近商场

### 2. 位置感知打车
- "从这里到机场" → 自动识别当前位置作为起点
- "打车去附近的地铁站" → 推荐最近的地铁站

### 3. 上下文对话
- "我在哪里？" → 返回当前位置信息
- "这附近有什么？" → 列出周边POI

## ⚠️ 注意事项

1. **API密钥**: 需要配置AMAP_API_KEY和BAILIAN_API_KEY
2. **网络连接**: 位置信息获取需要网络连接
3. **隐私保护**: 默认坐标可以根据需要调整
4. **性能考虑**: 位置上下文会在初始化时调用API

## 🔄 向后兼容

- ✅ 完全兼容现有的taxi_agent_system功能
- ✅ MainAgent接口保持不变
- ✅ 现有工具功能不受影响
- ✅ 可通过环境变量控制启用/禁用

---

**🌍 位置上下文功能让AI助手更加智能和贴近用户需求！**
