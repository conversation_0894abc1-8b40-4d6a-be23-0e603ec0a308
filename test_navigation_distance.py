#!/usr/bin/env python3
"""
测试高德地图导航距离和预估时间API功能
"""

import os
import sys
from amap_mcp_tools import AmapMCPTools, mcp_calculate_driving_route, mcp_calculate_poi_to_poi_route
from taxi_agent_system import EnhancedTaxiAgent

def test_coordinate_to_coordinate_route():
    """测试经纬度到经纬度的导航距离计算"""
    print("=== 测试经纬度到经纬度的导航距离计算 ===")
    
    # 测试北京天安门到北京西站的距离
    origin_lng = 116.397428  # 天安门经度
    origin_lat = 39.90923    # 天安门纬度
    dest_lng = 116.322056    # 北京西站经度
    dest_lat = 39.893729     # 北京西站纬度
    
    result = mcp_calculate_driving_route(origin_lng, origin_lat, dest_lng, dest_lat)
    
    if result.get("status"):
        data = result["data"]
        print(f"✅ 路径规划成功:")
        print(f"   起点: {data['origin']}")
        print(f"   终点: {data['destination']}")
        print(f"   距离: {data['distance']}米 ({data['distance']/1000:.1f}公里)")
        print(f"   时间: {data['duration']}秒 ({data['duration']//60}分{data['duration']%60}秒)")
        print(f"   过路费: {data['tolls']}元")
        print(f"   红绿灯: {data['traffic_lights']}个")
        print(f"   限行状态: {'有限行' if data['restriction'] else '无限行'}")
        print(f"   策略: {data['strategy']}")
    else:
        print(f"❌ 路径规划失败: {result.get('error')}")
    
    print()

def test_poi_to_poi_route():
    """测试POI名称到POI名称的导航距离计算"""
    print("=== 测试POI名称到POI名称的导航距离计算 ===")
    
    # 测试北京天安门到北京西站
    origin_poi = "北京天安门"
    dest_poi = "北京西站"
    
    result = mcp_calculate_poi_to_poi_route(origin_poi, dest_poi)
    
    if result.get("status"):
        data = result["data"]
        print(f"✅ POI路径规划成功:")
        print(f"   起点POI: {data['origin_poi']}")
        print(f"   终点POI: {data['destination_poi']}")
        print(f"   起点地址: {data['origin_address']}")
        print(f"   终点地址: {data['destination_address']}")
        print(f"   起点坐标: {data['origin_coords']}")
        print(f"   终点坐标: {data['destination_coords']}")
        print(f"   距离: {data['distance']}米 ({data['distance']/1000:.1f}公里)")
        print(f"   时间: {data['duration']}秒 ({data['duration']//60}分{data['duration']%60}秒)")
        print(f"   过路费: {data['tolls']}元")
        print(f"   红绿灯: {data['traffic_lights']}个")
        print(f"   限行状态: {'有限行' if data['restriction'] else '无限行'}")
    else:
        print(f"❌ POI路径规划失败: {result.get('error')}")
    
    print()

def test_different_strategies():
    """测试不同的路径规划策略"""
    print("=== 测试不同的路径规划策略 ===")
    
    origin_lng = 116.397428  # 天安门
    origin_lat = 39.90923
    dest_lng = 116.322056    # 北京西站
    dest_lat = 39.893729
    
    strategies = {
        10: "躲避拥堵，路程较短（推荐）",
        12: "躲避拥堵",
        13: "不走高速",
        14: "避免收费",
        19: "高速优先"
    }
    
    for strategy, description in strategies.items():
        print(f"策略 {strategy}: {description}")
        result = mcp_calculate_driving_route(origin_lng, origin_lat, dest_lng, dest_lat, strategy)
        
        if result.get("status"):
            data = result["data"]
            print(f"   距离: {data['distance']}米, 时间: {data['duration']//60}分{data['duration']%60}秒, 过路费: {data['tolls']}元")
        else:
            print(f"   ❌ 失败: {result.get('error')}")
        print()

def test_cross_city_route():
    """测试跨城市路径规划"""
    print("=== 测试跨城市路径规划 ===")
    
    # 测试北京到上海的距离（使用POI）
    origin_poi = "北京天安门"
    dest_poi = "上海外滩"
    
    result = mcp_calculate_poi_to_poi_route(origin_poi, dest_poi, "北京", "上海")
    
    if result.get("status"):
        data = result["data"]
        print(f"✅ 跨城路径规划成功:")
        print(f"   {data['origin_poi']} → {data['destination_poi']}")
        print(f"   距离: {data['distance']}米 ({data['distance']/1000:.0f}公里)")
        print(f"   时间: {data['duration']//3600}小时{(data['duration']%3600)//60}分钟")
        print(f"   过路费: {data['tolls']}元")
    else:
        print(f"❌ 跨城路径规划失败: {result.get('error')}")
    
    print()

def test_taxi_agent_integration():
    """测试与taxi_agent_system的集成"""
    print("=== 测试与taxi_agent_system的集成 ===")
    
    try:
        agent = EnhancedTaxiAgent()
        
        # 测试用户询问距离和时间的场景
        test_queries = [
            "从北京天安门到北京西站开车需要多长时间？",
            "计算一下从经纬度116.397428,39.90923到116.322056,39.893729的驾车距离",
            "我想知道从杭州西湖到杭州东站的路程和时间"
        ]
        
        for query in test_queries:
            print(f"用户询问: {query}")
            response = agent.process_message(query)
            print(f"系统回复: {response}")
            print()
            
    except Exception as e:
        print(f"❌ 集成测试失败: {e}")

def main():
    """主测试函数"""
    print("开始测试高德地图导航距离和预估时间API功能")
    print("=" * 60)
    
    # 检查API密钥
    if not os.getenv("AMAP_API_KEY"):
        print("❌ 请设置环境变量 AMAP_API_KEY")
        return
    
    try:
        # 基础功能测试
        test_coordinate_to_coordinate_route()
        test_poi_to_poi_route()
        
        # 高级功能测试
        test_different_strategies()
        test_cross_city_route()
        
        # 集成测试
        test_taxi_agent_integration()
        
        print("✅ 所有测试完成")
        
    except Exception as e:
        print(f"❌ 测试过程中出现错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
